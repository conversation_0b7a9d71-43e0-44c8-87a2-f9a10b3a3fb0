'use client';

import React, { useState, useMemo, useEffect, useContext } from 'react';
import { Box, Typography, Tooltip, Divider } from '@mui/material';
import { useTheme, useMediaQuery } from '@mui/material';
import { useRouter } from 'next/navigation';
import {
  fetchFromStorage,
  saveToStorage,
  removeFromStorage,
} from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import {
  checkOrganizationRole,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import AuthContext from '@/helper/authcontext';
import Ticket from '../Ticket';
import CustomSearch from '@/components/UI/CustomSearch';
import FilterListIcon from '@mui/icons-material/FilterList';
import CheckIcon from '@mui/icons-material/Check';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import AddIcon from '@mui/icons-material/Add';
import RightDrawer from '@/components/UI/RightDrawer';
import FilterComponent from '@/components/UI/FilterComponent';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomButton from '@/components/UI/CustomButton';
import ContentLoader from '@/components/UI/ContentLoader';
import NoDataView from '@/components/UI/NoDataView';
import CustomOrgPagination from '@/components/UI/customPagination';
import { supportTicketService } from '@/services/supportTicketService';
import { staticOptions } from '@/helper/common/staticOptions';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import './alltickets.scss';

export default function AllTicketsList() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(1500));
  const { userdata, setUserdata } = useContext(AuthContext);
  const [searchValue, setSearchValue] = useState('');
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  const [loading, setLoading] = useState(true);
  const [ticketsData, setTicketsData] = useState([]);

  // Pagination state - following Allergen pattern
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(12);
  const [totalCount, setTotalCount] = useState(0);

  const router = useRouter();

  // Filter related state - following recipe module pattern
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [filterData, setFilterData] = useState({
    status: '',
    priority: '',
    module: '',
    type: '',
    organization: '',
  });

  // Separate state for applied filters - following recipe module pattern
  const [filterDataApplied, setFilterDataApplied] = useState({
    status: '',
    priority: '',
    module: '',
    type: '',
    organization: '',
    searchValue: '',
  });

  // Organization list state for super_admin filter
  const [organizationOptions, setOrganizationOptions] = useState([]);
  const [isRestoringFilters, setIsRestoringFilters] = useState(false);

  // Function to fetch organization list for super_admin
  const getOrganizationList = async () => {
    if (!checkOrganizationRole('super_admin')) {
      return; // Only fetch for super_admin
    }

    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS.GET_ALL_ORGANIZATION_DATA + '?searchString=&page=1&size=1000'
      );
      if (status === 200) {
        const orgOptions =
          data?.data?.map((org) => ({
            label: org?.name || 'Unknown Organization',
            value: org?.id || org?.organization_id,
          })) || [];
        setOrganizationOptions(orgOptions);
      }
    } catch (error) {
      console.error('Error fetching organization list:', error);
      setOrganizationOptions([]);
    }
  };

  // API function to get tickets with filters - following recipe module pattern
  const getTicketsListData = async (
    search,
    page,
    limit,
    apiFilters,
    showLoader = true
  ) => {
    if (showLoader) {
      setLoading(true);
    }

    try {
      // Build filter object for API
      const filterParams = {
        ticket_status: apiFilters?.status || '',
        ticket_priority: apiFilters?.priority || '',
        ticket_module: apiFilters?.module || '',
        ticket_type: apiFilters?.type || '',
        assigned_to_user_id: apiFilters?.assigned_to_user_id || '',
        organization_id: apiFilters?.organization || '',
      };
      Object.keys(filterParams)?.forEach((key) => {
        if (
          filterParams[key] === undefined ||
          filterParams[key] === '' ||
          filterParams[key] === null
        ) {
          delete filterParams[key];
        }
      });

      const response = await supportTicketService.getTicketsList(
        search || '',
        page || 1,
        limit || 10,
        filterParams,
        null // sort - can be added later if needed
      );
      if (response?.tickets) {
        setTicketsData(response?.tickets);
        setTotalCount(response?.totalCount || 0);
      } else {
        setTicketsData([]);
        setTotalCount(0);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setTicketsData([]);
      setTotalCount(0);
    } finally {
      if (showLoader) {
        setTimeout(() => {
          setLoading(false);
        }, 100);
      }
    }
  };

  // Load saved filters from localStorage on mount and fetch organization list for super_admin
  useEffect(() => {
    const savedFilters = fetchFromStorage(identifiers?.SUPPORT_TICKET_FILTER);
    if (!savedFilters) {
      setSelectedFilters(filters?.slice(0, 4)?.map((filter) => filter?.key));
    } else {
      setSelectedFilters(savedFilters);
    }

    // Fetch organization list for super_admin users
    getOrganizationList();
  }, []);

  // Filter persistence functionality - following staff module pattern
  useEffect(() => {
    // Small delay to ensure component is fully mounted
    const timer = setTimeout(() => {
      const redirectData = fetchFromStorage(identifiers?.RedirectData);

      if (redirectData && redirectData?.IsFromUser && !isRestoringFilters) {
        setIsRestoringFilters(true);
        setCurrentPage(redirectData?.page || 1);

        // Ensure filter data structure is complete
        const restoredFilterData = {
          status: redirectData?.filterData?.status || '',
          priority: redirectData?.filterData?.priority || '',
          module: redirectData?.filterData?.module || '',
          type: redirectData?.filterData?.type || '',
          organization: redirectData?.filterData?.organization || '',
        };

        const restoredFilterDataApplied = {
          ...restoredFilterData,
          searchValue: redirectData?.searchValue || '',
        };

        setFilterData(restoredFilterData);
        setFilterDataApplied(restoredFilterDataApplied);
        setSearchValue(redirectData?.searchValue || '');
        setRowsPerPage(redirectData?.rowsPerPage || 12);

        // Clear the redirect data after using it to prevent re-triggering
        removeFromStorage(identifiers?.RedirectData);

        // Build proper filter structure for API
        const restoredFilters = {};
        const filterData = redirectData?.filterData || {};
        if (filterData?.status) restoredFilters.status = filterData.status;
        if (filterData?.priority)
          restoredFilters.priority = filterData.priority;
        if (filterData?.module) restoredFilters.module = filterData.module;
        if (filterData?.type) restoredFilters.type = filterData.type;
        if (filterData?.organization)
          restoredFilters.organization = filterData.organization;

        getTicketsListData?.(
          redirectData?.searchValue || '',
          redirectData?.page || 1,
          redirectData?.rowsPerPage || 12,
          restoredFilters,
          true
        );
      } else if (userdata && userdata?.IsFromUser && !isRestoringFilters) {
        setIsRestoringFilters(true);
        const fdata = userdata;
        setCurrentPage(fdata?.page || 1);

        // Ensure filter data structure is complete
        const userdataFilterData = {
          status: fdata?.filterData?.status || '',
          priority: fdata?.filterData?.priority || '',
          module: fdata?.filterData?.module || '',
          type: fdata?.filterData?.type || '',
          organization: fdata?.filterData?.organization || '',
        };

        const userdataFilterDataApplied = {
          ...userdataFilterData,
          searchValue: fdata?.searchValue || '',
        };

        setFilterData(userdataFilterData);
        setFilterDataApplied(userdataFilterDataApplied);
        setSearchValue(fdata?.searchValue || '');
        setRowsPerPage(fdata?.rowsPerPage || 12);

        // Clear the userdata flag after using it
        setUserdata({ ...userdata, IsFromUser: false });

        // Build proper filter structure for API
        const userdataFilters = {};
        const filterData = fdata?.filterData || {};
        if (filterData?.status) userdataFilters.status = filterData.status;
        if (filterData?.priority)
          userdataFilters.priority = filterData.priority;
        if (filterData?.module) userdataFilters.module = filterData.module;
        if (filterData?.type) userdataFilters.type = filterData.type;
        if (filterData?.organization)
          userdataFilters.organization = filterData.organization;

        getTicketsListData?.(
          fdata?.searchValue || '',
          fdata?.page || 1,
          fdata?.rowsPerPage || 12,
          userdataFilters,
          true
        );
      } else if (
        !redirectData &&
        !userdata?.IsFromUser &&
        !isRestoringFilters
      ) {
        // Check if there are any active filters before loading initial data
        const hasActiveFilters =
          filterData?.status ||
          filterData?.priority ||
          filterData?.module ||
          filterData?.type ||
          filterData?.organization ||
          searchValue;

        if (!hasActiveFilters) {
          // Only load initial data if no filters are active and no restoration is happening
          getTicketsListData?.('', 1, rowsPerPage, {}, true);
        } else {
          // If there are active filters on mount, apply them immediately
          const apiFilters = {};
          if (filterData?.status && filterData.status !== '') {
            apiFilters.status = filterData.status;
          }
          if (filterData?.priority && filterData.priority !== '') {
            apiFilters.priority = filterData.priority;
          }
          if (filterData?.module && filterData.module !== '') {
            apiFilters.module = filterData.module;
          }
          if (filterData?.type && filterData.type !== '') {
            apiFilters.type = filterData.type;
          }
          if (filterData?.organization && filterData.organization !== '') {
            apiFilters.organization = filterData.organization;
          }

          // Apply filters immediately on mount
          setFilterDataApplied({
            status: filterData?.status || '',
            priority: filterData?.priority || '',
            module: filterData?.module || '',
            type: filterData?.type || '',
            organization: filterData?.organization || '',
            searchValue: searchValue || '',
          });

          getTicketsListData?.(
            searchValue || '',
            1,
            rowsPerPage,
            apiFilters,
            true
          );
        }
      }
    }, 100); // 100ms delay

    return () => clearTimeout(timer);
  }, []); // Empty dependency array to run only once on mount

  // Cleanup effect to reset restoration flag when component unmounts
  useEffect(() => {
    return () => {
      setIsRestoringFilters(false);
    };
  }, []);

  // Effect to handle search value changes only (not other filters)
  useEffect(() => {
    // Only auto-apply search when search value changes and we're not restoring filters
    if (!isRestoringFilters && searchValue !== filterDataApplied.searchValue) {
      // Add a small delay for search debouncing
      const searchTimer = setTimeout(() => {
        // Auto-apply search immediately but keep other filters as they were applied
        const newFilterData = {
          status: filterDataApplied?.status || '',
          priority: filterDataApplied?.priority || '',
          module: filterDataApplied?.module || '',
          type: filterDataApplied?.type || '',
          organization: filterDataApplied?.organization || '',
          searchValue: searchValue || '',
        };

        // Update applied filter state
        setFilterDataApplied(newFilterData);
        setCurrentPage(1); // Reset to first page when searching

        // Build proper filter structure for API using applied filters
        const apiFilters = {};
        if (newFilterData?.status && newFilterData.status !== '') {
          apiFilters.status = newFilterData.status;
        }
        if (newFilterData?.priority && newFilterData.priority !== '') {
          apiFilters.priority = newFilterData.priority;
        }
        if (newFilterData?.module && newFilterData.module !== '') {
          apiFilters.module = newFilterData.module;
        }
        if (newFilterData?.type && newFilterData.type !== '') {
          apiFilters.type = newFilterData.type;
        }
        if (newFilterData?.organization && newFilterData.organization !== '') {
          apiFilters.organization = newFilterData.organization;
        }

        // Call API with proper filter structure
        getTicketsListData?.(
          searchValue || '',
          1,
          rowsPerPage,
          apiFilters,
          true
        );
      }, 300); // 300ms delay for search debouncing

      return () => clearTimeout(searchTimer);
    }
  }, [searchValue, isRestoringFilters, filterDataApplied]);

  // Filter configuration
  const filters = useMemo(
    () => [
      {
        key: 'search',
        label: 'Search',
        options: [],
        permission: true,
      },
      {
        key: 'status',
        label: 'Status',
        options: staticOptions.SUPPORT_TICKET_STATUS_OPTIONS,
        permission: true,
      },
      {
        key: 'priority',
        label: 'Priority',
        options: staticOptions.SUPPORT_TICKET_PRIORITY_OPTIONS,
        permission: true,
      },
      {
        key: 'module',
        label: 'Module',
        options: staticOptions.SUPPORT_TICKET_MODULE_OPTIONS,
        permission: true,
      },
      {
        key: 'type',
        label: 'Type',
        options: staticOptions.SUPPORT_TICKET_TYPE_OPTIONS,
        permission: true,
      },
      {
        key: 'organization',
        label: 'Organization',
        options: organizationOptions,
        permission: checkOrganizationRole('super_admin'),
      },
    ],
    [organizationOptions]
  );

  const handleTicketClick = (ticket) => {
    // Ensure we have the most current filter data
    const currentFilterData = {
      status: filterDataApplied?.status || '',
      priority: filterDataApplied?.priority || '',
      module: filterDataApplied?.module || '',
      type: filterDataApplied?.type || '',
      organization: filterDataApplied?.organization || '',
      searchValue: searchValue || '',
    };

    // Save current filter state before navigating
    const redirectData = {
      IsFromUser: true,
      page: currentPage,
      filterData: currentFilterData,
      searchValue: searchValue || '',
      rowsPerPage: rowsPerPage,
    };

    // Save to localStorage
    saveToStorage(identifiers?.RedirectData, redirectData);

    // Also save to userdata context as backup
    setUserdata({
      ...userdata,
      IsFromUser: true,
      page: currentPage,
      filterData: currentFilterData,
      searchValue: searchValue || '',
      rowsPerPage: rowsPerPage,
    });

    // Navigate to main support ticket page with ticket ID as query parameter
    router.push(`/support-ticket?id=${ticket?.id}`);
  };

  const handleTicketDelete = async () => {
    try {
      // Calculate the new total count
      const newTotalCount = Math.max(0, totalCount - 1);
      setTotalCount(newTotalCount);

      // Calculate if we need to adjust pagination
      const totalPages = Math.ceil(newTotalCount / rowsPerPage);

      // Prepare filters for API call
      const filters = {};
      if (filterDataApplied?.status) filters.status = filterDataApplied.status;
      if (filterDataApplied?.priority)
        filters.priority = filterDataApplied.priority;
      if (filterDataApplied?.module) filters.module = filterDataApplied.module;
      if (filterDataApplied?.type) filters.type = filterDataApplied.type;
      if (filterDataApplied?.organization)
        filters.organization = filterDataApplied.organization;

      const searchValue = filterDataApplied?.searchValue || '';

      // If current page is beyond the new total pages, go to the last available page
      if (currentPage > totalPages && totalPages > 0) {
        // Navigate to the last page
        await getTicketsListData?.(
          searchValue,
          totalPages,
          rowsPerPage,
          filters,
          false
        );
        setCurrentPage(totalPages);
      } else if (ticketsData?.length === 1 && currentPage > 1) {
        // If current page will become empty and we're not on page 1, go to previous page
        await getTicketsListData?.(
          searchValue,
          currentPage - 1,
          rowsPerPage,
          filters,
          false
        );
        setCurrentPage(currentPage - 1);
      } else {
        // Refresh current page data
        await getTicketsListData?.(
          searchValue,
          currentPage,
          rowsPerPage,
          filters,
          false
        );
      }
    } catch (error) {
      console.error('Error handling ticket deletion:', error);
      // If there's an error, refresh the current page data
      const filters = {};
      if (filterDataApplied?.status) filters.status = filterDataApplied?.status;
      if (filterDataApplied?.priority)
        filters.priority = filterDataApplied?.priority;
      if (filterDataApplied?.module) filters.module = filterDataApplied?.module;
      if (filterDataApplied?.type) filters.type = filterDataApplied?.type;
      if (filterDataApplied?.organization)
        filters.organization = filterDataApplied?.organization;

      await getTicketsListData?.(
        filterDataApplied?.searchValue || '',
        currentPage,
        rowsPerPage,
        filters,
        false
      );
    }
  };

  //   const handleTicketClick = (ticket) => {
  //   // Only allow navigation for super_admin
  //   if (checkOrganizationRole('super_admin')) {
  //     router.push(`/support-ticket?id=${ticket.id}`);
  //   }
  // };

  // Filter functions - following recipe module pattern
  const toggleFilter = (key) => {
    setSelectedFilters((prevFilters) => {
      if (prevFilters?.includes(key)) {
        return prevFilters?.filter((item) => item !== key);
      } else {
        const index = filters.findIndex((filter) => filter?.key === key);
        const newFilters = [...prevFilters];
        newFilters?.splice(index, 0, key);
        return newFilters;
      }
    });
  };

  const getFirstFourFilters = () => {
    const savedFilters = fetchFromStorage(identifiers?.SUPPORT_TICKET_FILTER);
    setSelectedFilters(savedFilters?.slice(0, 4));
    saveToStorage(
      identifiers?.SUPPORT_TICKET_FILTER,
      savedFilters?.slice(0, 4)
    );
  };

  const saveLayout = () => {
    saveToStorage(identifiers?.SUPPORT_TICKET_FILTER, selectedFilters);
    setOpenFilterDrawer(false);
  };

  const handleKeyPress = (event) => {
    if (event?.key === 'Enter') {
      handleApplyFilter();
      setOpenFilterDrawer(false);
    }
  };

  const handleApplyFilter = () => {
    setIsRestoringFilters(false); // Reset flag when manually applying filters
    const newFilterData = {
      status: filterData?.status || '',
      priority: filterData?.priority || '',
      module: filterData?.module || '',
      type: filterData?.type || '',
      organization: filterData?.organization || '',
      searchValue: searchValue || '',
    };

    // Update applied filter state immediately
    setFilterDataApplied(newFilterData);
    setCurrentPage(1); // Reset to first page when applying filters

    // Build proper filter structure for API
    const apiFilters = {};
    if (newFilterData?.status && newFilterData.status !== '') {
      apiFilters.status = newFilterData.status;
    }
    if (newFilterData?.priority && newFilterData.priority !== '') {
      apiFilters.priority = newFilterData.priority;
    }
    if (newFilterData?.module && newFilterData.module !== '') {
      apiFilters.module = newFilterData.module;
    }
    if (newFilterData?.type && newFilterData.type !== '') {
      apiFilters.type = newFilterData.type;
    }
    if (newFilterData?.organization && newFilterData.organization !== '') {
      apiFilters.organization = newFilterData.organization;
    }

    // Call API with proper filter structure
    getTicketsListData?.(searchValue || '', 1, rowsPerPage, apiFilters, true);

    if (isMobile) {
      setOpenFilterDrawer?.(false);
    }
  };

  const handleClearFilter = () => {
    setIsRestoringFilters(false); // Reset flag when manually clearing filters
    setFilterData?.({
      status: '',
      priority: '',
      module: '',
      type: '',
      organization: '',
    });
    setSearchValue?.('');
    setFilterDataApplied({
      status: '',
      priority: '',
      module: '',
      type: '',
      organization: '',
      searchValue: '',
    });
    setCurrentPage(1); // Reset to first page when clearing filters

    // Call API directly with cleared filters
    getTicketsListData?.('', 1, rowsPerPage, {}, true);

    if (isMobile) {
      setOpenFilterDrawer?.(false);
    }
  };

  // Pagination handlers - following Allergen pattern
  const handlePageChange = async (newPage) => {
    setCurrentPage(newPage);
    const filters = {};
    if (filterDataApplied?.status) filters.status = filterDataApplied.status;
    if (filterDataApplied?.priority)
      filters.priority = filterDataApplied.priority;
    if (filterDataApplied?.module) filters.module = filterDataApplied.module;
    if (filterDataApplied?.type) filters.type = filterDataApplied.type;
    if (filterDataApplied?.organization)
      filters.organization = filterDataApplied.organization;

    await getTicketsListData?.(
      filterDataApplied?.searchValue || '',
      newPage,
      rowsPerPage,
      filters,
      false
    );
  };

  const handleRowsPerPageChange = async (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setCurrentPage(1);
    const filters = {};
    if (filterDataApplied?.status) filters.status = filterDataApplied.status;
    if (filterDataApplied?.priority)
      filters.priority = filterDataApplied.priority;
    if (filterDataApplied?.module) filters.module = filterDataApplied.module;
    if (filterDataApplied?.type) filters.type = filterDataApplied.type;
    if (filterDataApplied?.organization)
      filters.organization = filterDataApplied.organization;

    await getTicketsListData?.(
      filterDataApplied?.searchValue || '',
      1,
      newRowsPerPage,
      filters,
      false
    );
  };

  const handleCreateTicket = () => {
    router?.push?.('/support-ticket/create-ticket');
  };

  // Handle ticket status change and refresh list
  const handleTicketStatusChange = async (ticketId, newStatus) => {
    try {
      // Update the specific ticket's status in the current list
      const updatedTickets = ticketsData?.map((ticket) =>
        ticket?.id === ticketId
          ? { ...ticket, ticket_status: newStatus }
          : ticket
      );
      setTicketsData(updatedTickets);

      // Refresh the list with current filters to get updated data from server
      const filters = {};
      if (filterDataApplied?.status) filters.status = filterDataApplied.status;
      if (filterDataApplied?.priority)
        filters.priority = filterDataApplied.priority;
      if (filterDataApplied?.module) filters.module = filterDataApplied.module;
      if (filterDataApplied?.type) filters.type = filterDataApplied.type;

      // Call API to refresh the list
      await getTicketsListData?.(
        filterDataApplied?.searchValue || '',
        currentPage,
        rowsPerPage,
        filters,
        false
      );
    } catch {
      // Handle error silently or show user-friendly message
      setApiMessage(
        'error',
        'Failed to refresh ticket list. Please try again.'
      );
    }
  };

  return (
    <Box className="section-wrapper">
      <Box className="section-right">
        <Box className="section-right-tab-header">
          <Box className="d-flex align-center justify-space-between all-ticket-header-wrap">
            <Box className="section-right-title d-flex align-center gap-sm">
              <Typography className="sub-header-text">
                All Support Tickets
              </Typography>
            </Box>

            {/* Search and Filter Section - Same as Support Ticket */}
            <Box className="d-flex align-center gap-sm all-ticket-filter-wrap">
              <Box className="search-section-wrap">
                {!isMobile &&
                  selectedFilters?.map((key) => {
                    const filter = filters?.find((f) => f?.key === key);
                    return filter?.permission ? (
                      <React.Fragment key={key}>
                        {key === 'search' ? (
                          <Box className="search-section-fields">
                            <CustomSearch
                              fullWidth
                              setSearchValue={setSearchValue}
                              onKeyPress={handleKeyPress}
                              searchValue={searchValue || ''}
                            />
                          </Box>
                        ) : (
                          <Box className="search-section-fields">
                            <CustomSelect
                              placeholder={filter?.label || ''}
                              options={filter?.options || []}
                              value={
                                filter?.options?.find((opt) => {
                                  return opt?.value === filterData?.[key];
                                }) || ''
                              }
                              onChange={(e) =>
                                setFilterData?.({
                                  ...filterData,
                                  [key]: e?.value || '',
                                })
                              }
                              menuPortalTarget={document?.body}
                              styles={{
                                menuPortal: (base) => ({
                                  ...base,
                                  zIndex: 9999,
                                }),
                              }}
                            />
                          </Box>
                        )}
                      </React.Fragment>
                    ) : null;
                  })}

                {!isMobile && (
                  <>
                    <Box>
                      <CustomButton
                        isIconOnly
                        startIcon={
                          <Tooltip
                            title={
                              <Typography className="sub-title-text">
                                Apply Filter
                              </Typography>
                            }
                            arrow
                            classes={{
                              tooltip: 'info-tooltip-container',
                            }}
                          >
                            <CheckIcon />
                          </Tooltip>
                        }
                        onClick={handleApplyFilter}
                      />
                    </Box>
                    <Box>
                      <CustomButton
                        variant="outlined"
                        isIconOnly
                        startIcon={
                          <Tooltip
                            title={
                              <Typography className="sub-title-text">
                                Clear Filter
                              </Typography>
                            }
                            arrow
                            classes={{
                              tooltip: 'info-tooltip-container',
                            }}
                          >
                            <ClearOutlinedIcon />
                          </Tooltip>
                        }
                        onClick={handleClearFilter}
                      />
                    </Box>
                  </>
                )}
              </Box>
              <CustomButton
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Filters
                      </Typography>
                    }
                    classes={{ tooltip: 'info-tooltip-container' }}
                    arrow
                  >
                    <FilterListIcon />
                  </Tooltip>
                }
                onClick={() => {
                  setOpenFilterDrawer?.(true);
                }}
              />
              {/* Create New Ticket button - only for super_admin and org_master */}
              {(checkOrganizationRole('super_admin') ||
                checkOrganizationRole('org_master')) && (
                <CustomButton
                  title="Create New Ticket"
                  startIcon={<AddIcon />}
                  onClick={handleCreateTicket}
                />
              )}
            </Box>
          </Box>
          <Divider />
        </Box>

        <Box className="section-right-content">
          <Box className="all-tickets-container-wrap">
            <Box className="all-tickets-container">
              {loading ? (
                <ContentLoader />
              ) : (
                <>
                  {/* Tickets List */}
                  <Box className="tickets-grid">
                    {ticketsData?.map((ticket) => (
                      <Box key={ticket?.id} className="ticket-card-wrapper">
                        <Box className="all-tickets-list-container">
                          <Ticket
                            ticketsList={[ticket]}
                            selectedTicket={null}
                            onTicketClick={handleTicketClick}
                            onTicketDelete={handleTicketDelete}
                            onTicketStatusChange={handleTicketStatusChange}
                            hideDropdown={true}
                            hideStatusFilter={true} // Hide status filter in All Support Tickets page
                          />
                        </Box>
                      </Box>
                    ))}
                  </Box>

                  {/* No tickets message */}
                  {ticketsData?.length === 0 && !loading && (
                    <Box className="no-data d-flex align-center justify-center">
                      <NoDataView
                        title="No Support Tickets Found"
                        description="No tickets match the current filter criteria. Try adjusting your filters or create a new ticket."
                      />
                    </Box>
                  )}

                  {/* Pagination */}
                  {ticketsData?.length > 0 && (
                    <Box className="all-ticket-pagination-wrap">
                      <CustomOrgPagination
                        currentPage={currentPage}
                        totalCount={totalCount}
                        rowsPerPage={rowsPerPage}
                        onPageChange={handlePageChange}
                        OnRowPerPage={handleRowsPerPageChange}
                        isInvited={true}
                      />
                    </Box>
                  )}
                </>
              )}

              {/* Filter Drawer */}
              <Box className="drawer-wrap">
                <RightDrawer
                  className="filter-options-drawer"
                  anchor="right"
                  open={openFilterDrawer}
                  onClose={() => setOpenFilterDrawer?.(false)}
                  title="Filter"
                  content={
                    <Box>
                      <FilterComponent
                        filters={filters || []}
                        filterData={filterData || {}}
                        setFilterData={setFilterData}
                        selectedFilters={selectedFilters || []}
                        toggleFilter={toggleFilter}
                        saveLayout={saveLayout}
                        setOpenFilterDrawer={setOpenFilterDrawer}
                        setSelectedFilters={setSelectedFilters}
                        getFirstFourFilters={getFirstFourFilters}
                        setSearchValue={setSearchValue}
                        searchValue={searchValue || ''}
                        handleKeyPress={handleKeyPress}
                        isMobile={isMobile}
                        handleApplyFilter={handleApplyFilter}
                        handleClearFilter={handleClearFilter}
                      />
                    </Box>
                  }
                />
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}
